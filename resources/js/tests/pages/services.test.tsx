import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import Services from '@/pages/public/services';
import { router } from '@inertiajs/react';
import { SELECT_ALL_CATEGORIES } from '@/lib/utils';

// Mock the router
vi.mock('@inertiajs/react', async () => {
  const actual = await vi.importActual('@inertiajs/react');
  return {
    ...actual,
    router: {
      get: vi.fn(),
    },
  };
});

describe('Services Page', () => {
  const mockServices = [
    {
      id: 1,
      title: 'Meta Ads Management',
      slug: 'meta-ads-management',
      description: 'Professional Meta advertising management',
      category: 'Meta Advertising',
      price_range: '$500 - $2000',
      features: ['Campaign Setup', 'Optimization', 'Reporting'],
      is_active: true,
    },
    {
      id: 2,
      title: 'Facebook Pixel Setup',
      slug: 'facebook-pixel-setup',
      description: 'Professional Facebook Pixel implementation',
      category: 'Tracking & Analytics',
      price_range: '$200 - $500',
      features: ['Pixel Installation', 'Event Setup', 'Testing'],
      is_active: true,
    },
    {
      id: 3,
      title: 'Google Analytics Setup',
      slug: 'google-analytics-setup',
      description: 'Professional Google Analytics implementation',
      category: 'Tracking & Analytics',
      price_range: '$300 - $600',
      features: ['GA4 Setup', 'Goal Configuration', 'Reporting'],
      is_active: true,
    },
  ];

  const mockCategories = ['Meta Advertising', 'Tracking & Analytics', 'Consulting'];

  const mockFilters = {
    search: '',
    category: SELECT_ALL_CATEGORIES,
    sort_by: 'sort_order',
    sort_direction: 'asc',
  };

  const mockMeta = {
    title: 'Our Services - ConvertOKit',
    description: 'Professional Meta advertising and web analytics services',
    canonical: 'https://convertokit.com/services',
    og_title: 'Our Services - ConvertOKit',
    og_description: 'Professional Meta advertising and web analytics services',
    twitter_card: 'summary_large_image',
  };

  const defaultProps = {
    services: mockServices,
    categories: mockCategories,
    filters: mockFilters,
    meta: mockMeta,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders all services correctly', () => {
    render(<Services {...defaultProps} />);

    expect(screen.getByText('Meta Ads Management')).toBeInTheDocument();
    expect(screen.getByText('Facebook Pixel Setup')).toBeInTheDocument();
    expect(screen.getByText('Google Analytics Setup')).toBeInTheDocument();
  });

  it('renders search input with placeholder', () => {
    render(<Services {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search services...');
    expect(searchInput).toBeInTheDocument();
  });

  it('renders category filter dropdown', () => {
    render(<Services {...defaultProps} />);

    expect(screen.getByText('All Categories')).toBeInTheDocument();
  });

  it('renders sort options dropdown', () => {
    render(<Services {...defaultProps} />);

    const sortDropdown = screen.getByDisplayValue('Default Order');
    expect(sortDropdown).toBeInTheDocument();
  });

  it('handles search input changes with debounce', async () => {
    const user = userEvent.setup();
    render(<Services {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search services...');
    
    await user.type(searchInput, 'Meta');

    // Should update the input value immediately
    expect(searchInput).toHaveValue('Meta');

    // Should call router.get after debounce delay
    await waitFor(() => {
      expect(router.get).toHaveBeenCalledWith(
        '/services',
        expect.objectContaining({ search: 'Meta' }),
        expect.objectContaining({
          preserveState: true,
          preserveScroll: true,
        })
      );
    }, { timeout: 1000 });
  });

  it('handles category filter changes', async () => {
    const user = userEvent.setup();
    render(<Services {...defaultProps} />);

    // Find and click the category dropdown
    const categoryTrigger = screen.getByText('All Categories');
    await user.click(categoryTrigger);

    // Select a category
    const metaCategory = screen.getByText('Meta Advertising');
    await user.click(metaCategory);

    await waitFor(() => {
      expect(router.get).toHaveBeenCalledWith(
        '/services',
        expect.objectContaining({ category: 'Meta Advertising' }),
        expect.objectContaining({
          preserveState: true,
          preserveScroll: true,
        })
      );
    });
  });

  it('handles sort option changes', async () => {
    const user = userEvent.setup();
    render(<Services {...defaultProps} />);

    // Find and click the sort dropdown
    const sortTrigger = screen.getByDisplayValue('Default Order');
    await user.click(sortTrigger);

    // Select a sort option
    const nameSort = screen.getByText('Name (A-Z)');
    await user.click(nameSort);

    await waitFor(() => {
      expect(router.get).toHaveBeenCalledWith(
        '/services',
        expect.objectContaining({ 
          sort_by: 'title',
          sort_direction: 'asc'
        }),
        expect.objectContaining({
          preserveState: true,
          preserveScroll: true,
        })
      );
    });
  });

  it('displays active filters when filters are applied', () => {
    const propsWithFilters = {
      ...defaultProps,
      filters: {
        search: 'Meta',
        category: 'Meta Advertising',
        sort_by: 'title',
        sort_direction: 'asc',
      },
    };

    render(<Services {...propsWithFilters} />);

    expect(screen.getByText('Search: "Meta"')).toBeInTheDocument();
    expect(screen.getByText('Category: Meta Advertising')).toBeInTheDocument();
  });

  it('shows clear filters button when filters are active', () => {
    const propsWithFilters = {
      ...defaultProps,
      filters: {
        search: 'Meta',
        category: 'Meta Advertising',
        sort_by: 'title',
        sort_direction: 'asc',
      },
    };

    render(<Services {...propsWithFilters} />);

    const clearButton = screen.getByText('Clear');
    expect(clearButton).toBeInTheDocument();
  });

  it('handles clear filters button click', async () => {
    const user = userEvent.setup();
    const propsWithFilters = {
      ...defaultProps,
      filters: {
        search: 'Meta',
        category: 'Meta Advertising',
        sort_by: 'title',
        sort_direction: 'asc',
      },
    };

    render(<Services {...propsWithFilters} />);

    const clearButton = screen.getByText('Clear');
    await user.click(clearButton);

    expect(router.get).toHaveBeenCalledWith(
      '/services',
      {},
      expect.objectContaining({
        preserveState: true,
        preserveScroll: true,
      })
    );
  });

  it('displays no services found message when services array is empty', () => {
    const propsWithNoServices = {
      ...defaultProps,
      services: [],
    };

    render(<Services {...propsWithNoServices} />);

    expect(screen.getByText('No services found')).toBeInTheDocument();
    expect(screen.getByText('Try adjusting your search criteria or browse all services.')).toBeInTheDocument();
    expect(screen.getByText('View All Services')).toBeInTheDocument();
  });

  it('shows filtered results header when filters are active', () => {
    const propsWithFilters = {
      ...defaultProps,
      filters: {
        search: 'Meta',
        category: '',
        sort_by: 'sort_order',
        sort_direction: 'asc',
      },
    };

    render(<Services {...propsWithFilters} />);

    expect(screen.getByText('Search Results')).toBeInTheDocument();
    expect(screen.getByText('3 services found')).toBeInTheDocument();
  });

  it('shows default header when no filters are active', () => {
    render(<Services {...defaultProps} />);

    expect(screen.getByText('Our Services')).toBeInTheDocument();
    expect(screen.getByText('3 services found')).toBeInTheDocument();
  });

  it('groups services by category when no filters are active', () => {
    render(<Services {...defaultProps} />);

    expect(screen.getByText('Meta Advertising')).toBeInTheDocument();
    expect(screen.getByText('Tracking & Analytics')).toBeInTheDocument();
  });

  it('shows services as simple grid when filters are active', () => {
    const propsWithFilters = {
      ...defaultProps,
      filters: {
        search: 'Meta',
        category: '',
        sort_by: 'sort_order',
        sort_direction: 'asc',
      },
    };

    render(<Services {...propsWithFilters} />);

    // Should not show category headers when filters are active
    expect(screen.queryByText('Meta Advertising')).not.toBeInTheDocument();
    expect(screen.queryByText('Tracking & Analytics')).not.toBeInTheDocument();
  });

  it('renders service cards with correct information', () => {
    render(<Services {...defaultProps} />);

    // Check for service titles
    expect(screen.getByText('Meta Ads Management')).toBeInTheDocument();
    expect(screen.getByText('Facebook Pixel Setup')).toBeInTheDocument();

    // Check for service descriptions
    expect(screen.getByText('Professional Meta advertising management')).toBeInTheDocument();
    expect(screen.getByText('Professional Facebook Pixel implementation')).toBeInTheDocument();

    // Check for price ranges
    expect(screen.getByText('$500 - $2000')).toBeInTheDocument();
    expect(screen.getByText('$200 - $500')).toBeInTheDocument();
  });
});
